import discord
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
import io
import asyncio
import os
import time
from collections import defaultdict
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

intents = discord.Intents.default()
intents.message_content = True
intents.guilds = True
intents.messages = True
intents.members = True

bot = commands.Bot(command_prefix="!!", intents=intents)

# 🔧 CONFIGURATION
SUPPORT_ROLE_ID = 1060628081359982714  # Replace with your support role ID
TICKET_CATEGORY_ID = 451810734129676300  # Replace with category ID where threads should be created

# 🛡️ SPAM PROTECTION SETTINGS
TICKET_COOLDOWN = 300  # 5 minutes cooldown between tickets (in seconds)
MAX_TICKETS_PER_USER = 1  # Maximum active tickets per user

# 📊 TRACKING DICTIONARIES
user_cooldowns = {}  # {user_id: last_ticket_time}
active_tickets = defaultdict(list)  # {user_id: [thread_ids]}
creating_tickets = set()  # {user_id} - users currently creating tickets

@bot.event
async def on_ready():
    print(f"Bot is ready. Logged in as {bot.user}")
    # Clean up any stale ticket tracking on startup
    await cleanup_stale_tickets()

# 🛡️ SPAM PROTECTION & TICKET TRACKING FUNCTIONS
async def cleanup_stale_tickets():
    """Clean up tracking for deleted threads on bot startup"""
    category = bot.get_channel(TICKET_CATEGORY_ID)
    if not category:
        return

    # Get all current thread IDs in the category
    current_threads = set()
    for channel in category.channels:
        if isinstance(channel, discord.TextChannel):
            for thread in channel.threads:
                if thread.name.startswith("ticket-"):
                    current_threads.add(thread.id)

    # Clean up tracking for non-existent threads
    for user_id in list(active_tickets.keys()):
        active_tickets[user_id] = [tid for tid in active_tickets[user_id] if tid in current_threads]
        if not active_tickets[user_id]:
            del active_tickets[user_id]

def is_user_on_cooldown(user_id):
    """Check if user is on cooldown"""
    if user_id not in user_cooldowns:
        return False

    time_since_last = time.time() - user_cooldowns[user_id]
    return time_since_last < TICKET_COOLDOWN

def get_cooldown_remaining(user_id):
    """Get remaining cooldown time in seconds"""
    if user_id not in user_cooldowns:
        return 0

    time_since_last = time.time() - user_cooldowns[user_id]
    remaining = TICKET_COOLDOWN - time_since_last
    return max(0, remaining)

def has_active_tickets(user_id):
    """Check if user has active tickets or is currently creating one"""
    active_count = len(active_tickets.get(user_id, []))
    is_creating = user_id in creating_tickets
    return active_count >= MAX_TICKETS_PER_USER or is_creating

def add_user_ticket(user_id, thread_id):
    """Add a ticket to user's active tickets"""
    active_tickets[user_id].append(thread_id)
    user_cooldowns[user_id] = time.time()

def remove_user_ticket(user_id, thread_id):
    """Remove a ticket from user's active tickets"""
    if user_id in active_tickets:
        if thread_id in active_tickets[user_id]:
            active_tickets[user_id].remove(thread_id)
        if not active_tickets[user_id]:
            del active_tickets[user_id]

def start_ticket_creation(user_id):
    """Mark user as currently creating a ticket"""
    creating_tickets.add(user_id)

def finish_ticket_creation(user_id):
    """Remove user from ticket creation tracking"""
    creating_tickets.discard(user_id)

# View for ticket management buttons
class TicketManagementView(View):
    def __init__(self):
        super().__init__(timeout=None)
        self.add_item(SaveTranscriptButton())
        self.add_item(CloseTicketButton())

class SaveTranscriptButton(Button):
    def __init__(self):
        super().__init__(label="� Save Transcript", style=discord.ButtonStyle.secondary)

    async def callback(self, interaction: discord.Interaction):
        user_roles = [role.id for role in interaction.user.roles]
        if SUPPORT_ROLE_ID not in user_roles:
            await interaction.response.send_message("❌ You don't have permission to save transcripts.", ephemeral=True)
            return

        thread = interaction.channel
        transcript = await generate_transcript(thread)
        file = discord.File(fp=io.StringIO(transcript), filename=f"{thread.name}-transcript.txt")

        # Send transcript to the person requesting it via DM
        try:
            await interaction.user.send(
                f"📄 **Ticket Transcript**: {thread.name}\n"
                f"Requested by: {interaction.user.mention}\n"
                f"Generated at: {discord.utils.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}",
                file=file
            )
            await interaction.response.send_message("📄 Transcript sent to your DMs!", ephemeral=True)
        except discord.Forbidden:
            # Fallback: send in thread if DM fails
            file_fallback = discord.File(fp=io.StringIO(transcript), filename=f"{thread.name}-transcript.txt")
            await interaction.response.send_message(
                "⚠️ Couldn't send DM. Here's the transcript (please enable DMs for future use):",
                file=file_fallback,
                ephemeral=True
            )
        except Exception as e:
            await interaction.response.send_message(f"❌ Error generating transcript: {str(e)}", ephemeral=True)

class CloseTicketButton(Button):
    def __init__(self):
        super().__init__(label="🔒 Close Ticket", style=discord.ButtonStyle.danger)

    async def callback(self, interaction: discord.Interaction):
        user_roles = [role.id for role in interaction.user.roles]
        if SUPPORT_ROLE_ID not in user_roles:
            await interaction.response.send_message("❌ You don't have permission to close tickets.", ephemeral=True)
            return

        thread = interaction.channel
        transcript = await generate_transcript(thread)
        file = discord.File(fp=io.StringIO(transcript), filename=f"{thread.name}-transcript.txt")

        # Send transcript to the person closing the ticket via DM
        try:
            await interaction.user.send(
                f"📄 **Ticket Transcript**: {thread.name}\n"
                f"Closed by: {interaction.user.mention}\n"
                f"Closed at: {discord.utils.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}",
                file=file
            )
            dm_status = "✅ Transcript sent to your DMs."
        except discord.Forbidden:
            dm_status = "⚠️ Couldn't send DM. Please enable DMs from server members."
        except Exception as e:
            dm_status = f"⚠️ Error sending DM: {str(e)}"

        await interaction.response.send_message(
            f"📄 Ticket will be closed in 5 seconds.\n{dm_status}"
        )

        # Find the ticket creator from the thread name and remove from tracking
        thread_name = thread.name
        if thread_name.startswith("ticket-"):
            # Extract user info from thread name to find the creator
            for user_id, ticket_list in active_tickets.items():
                if thread.id in ticket_list:
                    remove_user_ticket(user_id, thread.id)
                    break

        await asyncio.sleep(5)
        await thread.delete()

async def generate_transcript(thread):
    """Generate a transcript of the thread"""
    messages = [msg async for msg in thread.history(limit=None, oldest_first=True)]
    transcript_lines = []

    for msg in messages:
        timestamp = msg.created_at.strftime('%Y-%m-%d %H:%M:%S UTC')
        author = f"{msg.author.display_name} ({msg.author})"
        content = msg.content if msg.content else "[No text content]"

        # Handle attachments
        if msg.attachments:
            attachments = ", ".join([att.filename for att in msg.attachments])
            content += f" [Attachments: {attachments}]"

        transcript_lines.append(f"[{timestamp}] {author}: {content}")

    return "\n".join(transcript_lines)

@bot.event
async def on_message(message):
    # Ignore bot messages
    if message.author.bot:
        return

    # Check if message is exactly !!ticket (not part of another command)
    if message.content.strip() == "!!ticket":
        await message.delete()

        # Check spam protection
        user_id = message.author.id

        # Check if user is on cooldown
        if is_user_on_cooldown(user_id):
            remaining = get_cooldown_remaining(user_id)
            minutes = int(remaining // 60)
            seconds = int(remaining % 60)
            await message.channel.send(
                f"⏰ {message.author.mention} You're on cooldown! Please wait {minutes}m {seconds}s before creating another ticket.",
                delete_after=10
            )
            return  # Don't process as command

        # Check if user has active tickets or is already creating one
        if has_active_tickets(user_id):
            active_count = len(active_tickets.get(user_id, []))
            is_creating = user_id in creating_tickets

            if is_creating:
                await message.channel.send(
                    f"⚠️ {message.author.mention} You're already creating a ticket! Please wait for it to complete.",
                    delete_after=10
                )
            else:
                await message.channel.send(
                    f"🎫 {message.author.mention} You already have {active_count} active ticket(s). Please close your existing ticket(s) before creating a new one.",
                    delete_after=10
                )
            return  # Don't process as command

        # Mark user as creating a ticket IMMEDIATELY to prevent spam
        start_ticket_creation(user_id)

        try:
            await create_ticket(message.author, message.guild, message.channel)
        except Exception as e:
            # If ticket creation fails, remove the creation lock
            finish_ticket_creation(user_id)
            await message.channel.send(
                f"❌ {message.author.mention} Failed to create ticket: {str(e)}",
                delete_after=15
            )
        return  # Don't process as command since we handled the ticket creation

    # Process other commands (only if it wasn't a ticket request)
    await bot.process_commands(message)

@bot.command()
async def check_perms(ctx):
    """Check bot permissions in the ticket category"""
    if not ctx.author.guild_permissions.administrator:
        await ctx.send("❌ Only administrators can use this command.")
        return

    category = bot.get_channel(TICKET_CATEGORY_ID)
    if not category:
        await ctx.send("❌ Ticket category not found!")
        return

    # Check bot permissions in category
    bot_perms = category.permissions_for(ctx.guild.me)

    required_perms = [
        ('read_messages', bot_perms.read_messages),
        ('send_messages', bot_perms.send_messages),
        ('create_private_threads', bot_perms.create_private_threads),
        ('manage_threads', bot_perms.manage_threads),
        ('send_messages_in_threads', bot_perms.send_messages_in_threads),
        ('manage_messages', bot_perms.manage_messages)
    ]

    perm_status = []
    for perm_name, has_perm in required_perms:
        status = "✅" if has_perm else "❌"
        perm_status.append(f"{status} {perm_name}")

    embed = discord.Embed(
        title="🔧 Bot Permissions Check",
        description=f"**Category:** {category.name}\n\n" + "\n".join(perm_status),
        color=0x00ff00 if all(p[1] for p in required_perms) else 0xff0000
    )

    await ctx.send(embed=embed)

@bot.command()
async def ticket_stats(ctx):
    """Show ticket system statistics (Admin only)"""
    if not ctx.author.guild_permissions.administrator:
        await ctx.send("❌ Only administrators can use this command.")
        return

    # Count active tickets
    total_active = sum(len(tickets) for tickets in active_tickets.values())
    users_with_tickets = len(active_tickets)

    # Count users on cooldown
    current_time = time.time()
    users_on_cooldown = sum(1 for last_time in user_cooldowns.values()
                           if current_time - last_time < TICKET_COOLDOWN)

    # Count users currently creating tickets
    users_creating = len(creating_tickets)

    embed = discord.Embed(
        title="🎫 Ticket System Statistics",
        color=0x00ff00
    )
    embed.add_field(name="Active Tickets", value=f"{total_active}", inline=False)
    embed.add_field(name="Users on Cooldown", value=f"{users_on_cooldown}", inline=False)
    embed.add_field(name="Cooldown Duration", value=f"{TICKET_COOLDOWN//60} minutes", inline=False)
    embed.add_field(name="Max Tickets per User", value=f"{MAX_TICKETS_PER_USER}", inline=False)

    await ctx.send(embed=embed)

@bot.command()
async def reset_cooldown(ctx, user: discord.Member):
    """Reset a user's ticket cooldown (Admin only)"""
    if not ctx.author.guild_permissions.administrator:
        await ctx.send("❌ Only administrators can use this command.")
        return

    if user.id in user_cooldowns:
        del user_cooldowns[user.id]
        await ctx.send(f"✅ Cooldown reset for {user.mention}")
    else:
        await ctx.send(f"ℹ️ {user.mention} is not on cooldown.")

@bot.command()
async def force_close_tickets(ctx, user: discord.Member):
    """Force close all tickets for a user (Admin only)"""
    if not ctx.author.guild_permissions.administrator:
        await ctx.send("❌ Only administrators can use this command.")
        return

    if user.id not in active_tickets:
        await ctx.send(f"ℹ️ {user.mention} has no active tickets.")
        return

    ticket_ids = active_tickets[user.id].copy()
    closed_count = 0

    for thread_id in ticket_ids:
        thread = bot.get_channel(thread_id)
        if thread:
            try:
                await thread.delete()
                closed_count += 1
            except:
                pass
        remove_user_ticket(user.id, thread_id)

    await ctx.send(f"✅ Force closed {closed_count} ticket(s) for {user.mention}")

async def create_ticket(user, guild, original_channel):
    """Create a ticket thread for the user"""
    category = bot.get_channel(TICKET_CATEGORY_ID)

    if not category or not isinstance(category, discord.CategoryChannel):
        await original_channel.send("⚠️ Ticket category not found or misconfigured.", delete_after=10)
        return

    # Find a suitable channel in the category to create the thread
    # We'll use the first text channel in the category
    parent_channel = None
    for channel in category.channels:
        if isinstance(channel, discord.TextChannel):
            parent_channel = channel
            break

    if not parent_channel:
        await original_channel.send("⚠️ No suitable channel found in the ticket category.", delete_after=10)
        return

    try:
        # Create a thread from the parent channel
        thread = await parent_channel.create_thread(
            name=f"ticket-{user.name}-{user.discriminator}",
            type=discord.ChannelType.private_thread,
            invitable=False
        )

        # Add the ticket creator
        await thread.add_user(user)

        # Add all support role members
        support_role = guild.get_role(SUPPORT_ROLE_ID)
        support_members = []
        if support_role:
            for member in support_role.members:
                try:
                    await thread.add_user(member)
                    support_members.append(member)
                except discord.HTTPException:
                    print(f"Failed to add {member} to thread")

        # Create mentions for all thread participants
        mentions = [user.mention]
        if support_role:
            mentions.append(support_role.mention)

        # Send initial message with all mentions to ensure visibility
        initial_message = (
            f"🎫 **New Ticket Created**\n"
            f"**Created by:** {user.mention}\n"
            f"**Support Team:** {support_role.mention if support_role else 'No support role configured'}\n"
            f"**Thread Participants:** {', '.join([user.mention] + [m.mention for m in support_members])}\n\n"
            f"📋 This is a private support ticket. Use the buttons below to manage this ticket."
        )

        await thread.send(initial_message, view=TicketManagementView())

        # Add ticket to tracking and remove creation lock
        add_user_ticket(user.id, thread.id)
        finish_ticket_creation(user.id)

        # Send confirmation in original channel
        

    except discord.Forbidden:
        finish_ticket_creation(user.id)
        await original_channel.send(
            f"❌ {user.mention} I don't have permission to create threads in that category. "
            f"Please contact an administrator to check bot permissions.",
            delete_after=15
        )
    except discord.HTTPException as e:
        finish_ticket_creation(user.id)
        await original_channel.send(
            f"❌ {user.mention} Failed to create ticket: {str(e)}",
            delete_after=15
        )
    except Exception as e:
        finish_ticket_creation(user.id)
        await original_channel.send(
            f"❌ {user.mention} An unexpected error occurred: {str(e)}",
            delete_after=15
        )
        print(f"Ticket creation error: {e}")

# Optional: persistent view for restarting bot
@bot.event
async def setup_hook():
    bot.add_view(TicketManagementView())  # Register persistent button view on startup

# Run the bot
if __name__ == "__main__":
    token = os.getenv('DISCORD_BOT_TOKEN')
    if not token:
        print("❌ Error: DISCORD_BOT_TOKEN not found in environment variables!")
        print("Make sure you have a .env file with DISCORD_BOT_TOKEN=your_token_here")
        exit(1)

    bot.run(token)

