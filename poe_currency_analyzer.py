"""
Advanced Path of Exile Currency Analysis Module

This module provides advanced analysis capabilities for POE currency data,
including trend analysis, profit opportunity detection, and historical comparisons.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
import os
from poe_get_currency_prices import POENinjaAPI, CurrencyDataManager


class CurrencyAnalyzer:
    """Advanced analysis tools for POE currency data"""
    
    def __init__(self, data_manager: CurrencyDataManager):
        """
        Initialize the analyzer
        
        Args:
            data_manager: CurrencyDataManager instance for data access
        """
        self.data_manager = data_manager
        
    def get_historical_data(self, days: int = 7) -> pd.DataFrame:
        """
        Get historical currency data for analysis
        
        Args:
            days: Number of days of history to retrieve
            
        Returns:
            DataFrame with historical currency data
        """
        snapshots = self.data_manager.get_all_snapshots()
        
        if not snapshots:
            return pd.DataFrame()
            
        # Convert snapshots to DataFrame
        all_records = []
        
        for snapshot in snapshots:
            timestamp = datetime.fromisoformat(snapshot['timestamp'])
            
            # Skip if older than requested days
            if (datetime.now() - timestamp).days > days:
                continue
                
            # Process currency data
            if 'currency' in snapshot['data']:
                df = self.data_manager.convert_to_dataframe(
                    snapshot['data']['currency'], 'currency'
                )
                df['timestamp'] = timestamp
                all_records.append(df)
                
            # Process fragment data
            if 'fragment' in snapshot['data']:
                df = self.data_manager.convert_to_dataframe(
                    snapshot['data']['fragment'], 'fragment'
                )
                df['timestamp'] = timestamp
                all_records.append(df)
                
        if not all_records:
            return pd.DataFrame()
            
        return pd.concat(all_records, ignore_index=True)
    
    def calculate_price_changes(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate price changes over time for each currency
        
        Args:
            df: Historical currency data
            
        Returns:
            DataFrame with price change calculations
        """
        if df.empty:
            return df
            
        # Sort by currency and timestamp
        df = df.sort_values(['name', 'timestamp'])
        
        # Calculate price changes
        df['price_change'] = df.groupby('name')['chaos_equivalent'].pct_change() * 100
        df['price_change_abs'] = df.groupby('name')['chaos_equivalent'].diff()
        
        # Calculate moving averages
        df['ma_7'] = df.groupby('name')['chaos_equivalent'].rolling(window=7, min_periods=1).mean().reset_index(0, drop=True)
        df['ma_24'] = df.groupby('name')['chaos_equivalent'].rolling(window=24, min_periods=1).mean().reset_index(0, drop=True)
        
        return df
    
    def find_profit_opportunities(self, df: pd.DataFrame, min_change: float = 10.0) -> pd.DataFrame:
        """
        Find currencies with significant price movements that might indicate profit opportunities
        
        Args:
            df: Historical currency data with price changes
            min_change: Minimum percentage change to consider
            
        Returns:
            DataFrame with potential profit opportunities
        """
        if df.empty:
            return df
            
        # Get latest data for each currency
        latest_data = df.groupby('name').last().reset_index()
        
        # Filter for significant changes
        opportunities = latest_data[
            (abs(latest_data['price_change']) >= min_change) &
            (latest_data['chaos_equivalent'] > 1)  # Only consider valuable currencies
        ].copy()
        
        # Add opportunity type
        opportunities['opportunity_type'] = opportunities['price_change'].apply(
            lambda x: 'BUY' if x < -min_change else 'SELL' if x > min_change else 'HOLD'
        )
        
        # Sort by absolute price change
        opportunities = opportunities.sort_values('price_change', key=abs, ascending=False)
        
        return opportunities[['name', 'chaos_equivalent', 'price_change', 'price_change_abs', 'opportunity_type']]
    
    def get_top_movers(self, df: pd.DataFrame, n: int = 10) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Get top gaining and losing currencies
        
        Args:
            df: Historical currency data with price changes
            n: Number of top movers to return
            
        Returns:
            Tuple of (top_gainers, top_losers) DataFrames
        """
        if df.empty:
            return pd.DataFrame(), pd.DataFrame()
            
        latest_data = df.groupby('name').last().reset_index()
        
        # Filter out currencies with very low values to focus on meaningful changes
        filtered_data = latest_data[latest_data['chaos_equivalent'] > 0.1]
        
        top_gainers = filtered_data.nlargest(n, 'price_change')[
            ['name', 'chaos_equivalent', 'price_change', 'price_change_abs']
        ]
        
        top_losers = filtered_data.nsmallest(n, 'price_change')[
            ['name', 'chaos_equivalent', 'price_change', 'price_change_abs']
        ]
        
        return top_gainers, top_losers
    
    def create_price_trend_chart(self, currency_name: str, df: pd.DataFrame) -> go.Figure:
        """
        Create an interactive price trend chart for a specific currency
        
        Args:
            currency_name: Name of the currency to chart
            df: Historical currency data
            
        Returns:
            Plotly figure object
        """
        currency_data = df[df['name'] == currency_name].sort_values('timestamp')
        
        if currency_data.empty:
            fig = go.Figure()
            fig.add_annotation(text=f"No data found for {currency_name}", 
                             xref="paper", yref="paper", x=0.5, y=0.5)
            return fig
            
        fig = go.Figure()
        
        # Add price line
        fig.add_trace(go.Scatter(
            x=currency_data['timestamp'],
            y=currency_data['chaos_equivalent'],
            mode='lines+markers',
            name='Price',
            line=dict(color='blue', width=2),
            hovertemplate='<b>%{text}</b><br>Price: %{y:.2f} chaos<br>Time: %{x}<extra></extra>',
            text=[currency_name] * len(currency_data)
        ))
        
        # Add moving averages if available
        if 'ma_7' in currency_data.columns:
            fig.add_trace(go.Scatter(
                x=currency_data['timestamp'],
                y=currency_data['ma_7'],
                mode='lines',
                name='7-period MA',
                line=dict(color='orange', width=1, dash='dash'),
                opacity=0.7
            ))
            
        if 'ma_24' in currency_data.columns:
            fig.add_trace(go.Scatter(
                x=currency_data['timestamp'],
                y=currency_data['ma_24'],
                mode='lines',
                name='24-period MA',
                line=dict(color='red', width=1, dash='dot'),
                opacity=0.7
            ))
        
        fig.update_layout(
            title=f'{currency_name} Price Trend',
            xaxis_title='Time',
            yaxis_title='Price (Chaos Orbs)',
            hovermode='x unified',
            template='plotly_white'
        )
        
        return fig
    
    def create_market_overview_chart(self, df: pd.DataFrame) -> go.Figure:
        """
        Create a market overview chart showing top currencies by value
        
        Args:
            df: Historical currency data
            
        Returns:
            Plotly figure object
        """
        if df.empty:
            return go.Figure()
            
        # Get latest data for top currencies
        latest_data = df.groupby('name').last().reset_index()
        top_currencies = latest_data.nlargest(20, 'chaos_equivalent')
        
        # Create bar chart
        fig = go.Figure(data=[
            go.Bar(
                x=top_currencies['name'],
                y=top_currencies['chaos_equivalent'],
                text=top_currencies['chaos_equivalent'].round(2),
                textposition='auto',
                marker_color='lightblue'
            )
        ])
        
        fig.update_layout(
            title='Top 20 Currencies by Value',
            xaxis_title='Currency',
            yaxis_title='Value (Chaos Orbs)',
            xaxis_tickangle=-45,
            template='plotly_white'
        )
        
        return fig
    
    def generate_analysis_report(self, days: int = 7) -> Dict:
        """
        Generate a comprehensive analysis report
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        df = self.get_historical_data(days)
        
        if df.empty:
            return {"error": "No historical data available"}
            
        df = self.calculate_price_changes(df)
        
        # Get analysis results
        opportunities = self.find_profit_opportunities(df)
        top_gainers, top_losers = self.get_top_movers(df)
        
        # Calculate summary statistics
        latest_data = df.groupby('name').last().reset_index()
        
        report = {
            "analysis_date": datetime.now().isoformat(),
            "period_days": days,
            "total_currencies": len(latest_data),
            "total_market_value": latest_data['chaos_equivalent'].sum(),
            "average_price_change": latest_data['price_change'].mean(),
            "profit_opportunities": len(opportunities),
            "top_gainers": top_gainers.to_dict('records'),
            "top_losers": top_losers.to_dict('records'),
            "opportunities": opportunities.to_dict('records')
        }
        
        return report
