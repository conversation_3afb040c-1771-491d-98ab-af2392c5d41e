# 🎮 Path of Exile Currency Analysis Tool

A comprehensive, private currency price analysis tool for Path of Exile, similar to poe.ninja but focused on personal analytics and trading insights.

## 🌟 Features

- **Real-time Currency Prices**: Fetch current prices from poe.ninja API
- **Historical Price Analysis**: Track price trends over time
- **Trading Opportunity Detection**: Identify potential buy/sell opportunities
- **Interactive Charts**: Visualize price trends with Plotly
- **Web Dashboard**: Clean, responsive web interface
- **Automated Data Collection**: Schedule regular price updates
- **Export Functionality**: Export analysis data in JSON/CSV formats

## 📁 Project Structure

```
├── poe_get_currency_prices.py    # Core API client and data management
├── poe_currency_analyzer.py      # Advanced analysis and visualization
├── poe_data_collector.py         # Automated data collection
├── poe_app.py                    # Flask web application
├── poe_demo.py                   # Comprehensive demo script
├── poe_visualizations.py         # Chart generation
├── test_analyzer.py              # Testing utilities
├── templates/                    # HTML templates for web app
│   ├── poe_base.html
│   ├── poe_dashboard.html
│   ├── poe_analysis.html
│   └── poe_data_collection.html
├── poe_data/                     # Data storage directory
└── poe_charts/                   # Generated charts
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install requests pandas matplotlib plotly flask schedule
```

### 2. Run the Demo
```bash
python poe_demo.py
```

### 3. Start the Web Application
```bash
python poe_app.py
```
Access at: http://localhost:5006

### 4. Collect Data
```bash
# One-time collection
python poe_data_collector.py --once

# Continuous collection (every hour)
python poe_data_collector.py --interval 1
```

## 📊 Core Components

### POENinjaAPI Class
- Fetches data from poe.ninja API
- Supports all currency and item types
- Built-in rate limiting and error handling
- Configurable league selection

### CurrencyDataManager Class
- Manages local data storage
- Saves/loads historical snapshots
- Converts data to pandas DataFrames
- Handles data persistence

### CurrencyAnalyzer Class
- Advanced price trend analysis
- Profit opportunity detection
- Statistical calculations
- Interactive chart generation

## 🔧 Usage Examples

### Basic Price Fetching
```python
from poe_get_currency_prices import POENinjaAPI

api = POENinjaAPI(league="Standard")
currency_data = api.get_currency_data('currency')
print(f"Fetched {len(currency_data['lines'])} currencies")
```

### Analysis and Opportunities
```python
from poe_currency_analyzer import CurrencyAnalyzer
from poe_get_currency_prices import CurrencyDataManager

data_manager = CurrencyDataManager()
analyzer = CurrencyAnalyzer(data_manager)

# Generate analysis report
report = analyzer.generate_analysis_report(days=7)
print(f"Found {report['profit_opportunities']} trading opportunities")
```

### Data Collection
```python
from poe_data_collector import DataCollector

collector = DataCollector(league="Standard")
collector.collect_once()  # Collect data once
```

## 🌐 Web Interface

The Flask web application provides:

- **Dashboard**: Overview of current prices and market trends
- **Analysis**: Detailed analysis with configurable time periods
- **Charts**: Interactive visualizations of price trends
- **Data Collection**: Management of historical data snapshots

### API Endpoints

- `GET /api/current` - Current currency data
- `POST /api/collect` - Trigger data collection
- `GET /api/analysis?days=7` - Analysis report
- `GET /api/currency/<name>` - Specific currency details

## 📈 Analysis Features

### Price Change Detection
- Percentage and absolute price changes
- Moving averages (7-period and 24-period)
- Trend identification

### Trading Opportunities
- Configurable change thresholds
- Buy/sell recommendations
- Risk assessment based on volatility

### Market Overview
- Top currencies by value
- Biggest gainers and losers
- Market capitalization tracking

## 🎯 Trading Insights

The tool helps identify:
- **Undervalued currencies** with recent price drops
- **Trending items** with consistent growth
- **Volatile markets** for short-term trading
- **Stable investments** for long-term holding

## 📊 Visualization

### Interactive Charts
- Price trend lines with hover details
- Moving average overlays
- Market overview bar charts
- Responsive design for all devices

### Export Options
- JSON reports for programmatic analysis
- CSV exports for spreadsheet analysis
- HTML charts for sharing

## ⚙️ Configuration

### League Selection
```python
api = POENinjaAPI(league="Hardcore")  # or "Standard", "Settlers", etc.
```

### Data Collection Intervals
```bash
# Every 30 minutes
python poe_data_collector.py --interval 0.5

# Every 6 hours
python poe_data_collector.py --interval 6
```

### Analysis Periods
- 1 day for short-term trends
- 7 days for weekly analysis
- 30 days for long-term patterns

## 🔒 Privacy & Security

- **Local Data Storage**: All data stored locally, no external databases
- **No Personal Information**: Only public market data collected
- **Rate Limiting**: Respects poe.ninja API limits
- **Open Source**: Full transparency of data handling

## 🛠️ Advanced Usage

### Custom Analysis
```python
# Find currencies with >10% price change
opportunities = analyzer.find_profit_opportunities(df, min_change=10.0)

# Get top 20 movers
gainers, losers = analyzer.get_top_movers(df, n=20)

# Create custom charts
fig = analyzer.create_price_trend_chart("Mirror of Kalandra", df)
fig.show()
```

### Automated Alerts
Set up monitoring for specific currencies or price thresholds by extending the data collector.

## 📝 Notes

- **Data Source**: All price data comes from poe.ninja API
- **Update Frequency**: Recommended 1-2 hour intervals for best analysis
- **Storage**: Each snapshot is approximately 1-2MB
- **Performance**: Optimized for local analysis and visualization

## 🤝 Contributing

This is a personal analytics tool, but feel free to:
- Extend analysis algorithms
- Add new visualization types
- Improve the web interface
- Add support for additional leagues

## ⚠️ Disclaimer

This tool is not affiliated with or endorsed by Grinding Gear Games. Use trading insights at your own risk. Past performance does not guarantee future results.

---

**Happy Trading, Exile!** 🗡️⚔️
