"""
Test script for the POE Currency Analyzer
"""

from poe_get_currency_prices import CurrencyDataManager
from poe_currency_analyzer import CurrencyAnalyzer
import json

def main():
    print("Testing POE Currency Analyzer")
    print("=" * 40)
    
    # Initialize components
    data_manager = CurrencyDataManager()
    analyzer = CurrencyAnalyzer(data_manager)
    
    # Test historical data retrieval
    print("1. Testing historical data retrieval...")
    df = analyzer.get_historical_data(days=30)
    print(f"   Found {len(df)} historical records")
    
    if not df.empty:
        print(f"   Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        print(f"   Unique currencies: {df['name'].nunique()}")
        
        # Test price change calculations
        print("\n2. Testing price change calculations...")
        df = analyzer.calculate_price_changes(df)
        print(f"   Added price change columns")
        
        # Test profit opportunities
        print("\n3. Testing profit opportunity detection...")
        opportunities = analyzer.find_profit_opportunities(df, min_change=5.0)
        print(f"   Found {len(opportunities)} potential opportunities")
        
        if not opportunities.empty:
            print("\n   Top opportunities:")
            for _, opp in opportunities.head().iterrows():
                print(f"   {opp['name']:<25} {opp['opportunity_type']:<4} {opp['price_change']:>6.1f}% ({opp['chaos_equivalent']:>8.1f} chaos)")
        
        # Test top movers
        print("\n4. Testing top movers analysis...")
        gainers, losers = analyzer.get_top_movers(df, n=5)
        
        if not gainers.empty:
            print("\n   Top Gainers:")
            for _, gainer in gainers.iterrows():
                print(f"   {gainer['name']:<25} +{gainer['price_change']:>6.1f}% ({gainer['chaos_equivalent']:>8.1f} chaos)")
        
        if not losers.empty:
            print("\n   Top Losers:")
            for _, loser in losers.iterrows():
                print(f"   {loser['name']:<25} {loser['price_change']:>7.1f}% ({loser['chaos_equivalent']:>8.1f} chaos)")
        
        # Test analysis report
        print("\n5. Testing analysis report generation...")
        report = analyzer.generate_analysis_report(days=7)
        
        if 'error' not in report:
            print(f"   Analysis Date: {report['analysis_date']}")
            print(f"   Period: {report['period_days']} days")
            print(f"   Total Currencies: {report['total_currencies']}")
            print(f"   Total Market Value: {report['total_market_value']:,.0f} chaos")
            print(f"   Average Price Change: {report['average_price_change']:.2f}%")
            print(f"   Profit Opportunities: {report['profit_opportunities']}")
        else:
            print(f"   Error: {report['error']}")
    
    else:
        print("   No historical data available. Run the data collector first.")
    
    print("\nAnalyzer test completed!")

if __name__ == "__main__":
    main()
