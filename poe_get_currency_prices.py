"""
Path of Exile Currency Price Analysis Tool

This module provides functionality to fetch currency prices from poe.ninja API
and perform analysis for personal trading insights.

Features:
- Fetch current currency prices from poe.ninja
- Store historical price data
- Analyze price trends and changes
- Generate reports and visualizations
- Export data for further analysis

Author: Your Name
Date: 2025-01-18
"""

import requests
import pandas as pd
import json
import time
from datetime import datetime
from typing import Dict, List, Optional
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class POENinjaAPI:
    """Client for interacting with poe.ninja API"""

    BASE_URL = "https://poe.ninja/api/data"

    # Available data types and their endpoints
    CURRENCY_TYPES = {
        'currency': 'currencyoverview',
        'fragment': 'currencyoverview'
    }

    ITEM_TYPES = {
        'oil': 'itemoverview',
        'incubator': 'itemoverview',
        'scarab': 'itemoverview',
        'fossil': 'itemoverview',
        'resonator': 'itemoverview',
        'essence': 'itemoverview',
        'divination_card': 'itemoverview',
        'skill_gem': 'itemoverview',
        'base_type': 'itemoverview',
        'helmet_enchant': 'itemoverview',
        'unique_map': 'itemoverview',
        'map': 'itemoverview',
        'unique_jewel': 'itemoverview',
        'unique_flask': 'itemoverview',
        'unique_weapon': 'itemoverview',
        'unique_armour': 'itemoverview',
        'unique_accessory': 'itemoverview',
        'beast': 'itemoverview',
        'vial': 'itemoverview',
        'delirium_orb': 'itemoverview',
        'omen': 'itemoverview',
        'unique_relic': 'itemoverview',
        'cluster_jewel': 'itemoverview',
        'blighted_map': 'itemoverview',
        'blight_ravaged_map': 'itemoverview',
        'invitation': 'itemoverview',
        'memory': 'itemoverview',
        'coffin': 'itemoverview',
        'allflame_ember': 'itemoverview'
    }

    def __init__(self, league: str = "Standard", rate_limit_delay: float = 1.0):
        """
        Initialize the POE Ninja API client

        Args:
            league: The league to fetch data for (default: "Standard")
            rate_limit_delay: Delay between API calls in seconds
        """
        self.league = league
        self.rate_limit_delay = rate_limit_delay
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'POE-Currency-Analyzer/1.0 (Personal Analytics Tool)'
        })

    def _make_request(self, endpoint: str, params: Dict) -> Optional[Dict]:
        """
        Make a request to the poe.ninja API with error handling

        Args:
            endpoint: API endpoint to call
            params: Query parameters

        Returns:
            JSON response data or None if failed
        """
        try:
            url = f"{self.BASE_URL}/{endpoint}"
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()

            # Rate limiting
            time.sleep(self.rate_limit_delay)

            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            return None

    def get_currency_data(self, currency_type: str = 'currency') -> Optional[Dict]:
        """
        Fetch currency data from poe.ninja

        Args:
            currency_type: Type of currency data ('currency' or 'fragment')

        Returns:
            Currency data dictionary or None if failed
        """
        if currency_type not in self.CURRENCY_TYPES:
            logger.error(f"Invalid currency type: {currency_type}")
            return None

        endpoint = self.CURRENCY_TYPES[currency_type]
        params = {
            'league': self.league,
            'type': currency_type.title()
        }

        logger.info(f"Fetching {currency_type} data for league: {self.league}")
        return self._make_request(endpoint, params)

    def get_item_data(self, item_type: str) -> Optional[Dict]:
        """
        Fetch item data from poe.ninja

        Args:
            item_type: Type of item data to fetch

        Returns:
            Item data dictionary or None if failed
        """
        if item_type not in self.ITEM_TYPES:
            logger.error(f"Invalid item type: {item_type}")
            return None

        endpoint = self.ITEM_TYPES[item_type]
        # Convert snake_case to proper API format
        api_type = ''.join(word.capitalize() for word in item_type.split('_'))

        params = {
            'league': self.league,
            'type': api_type
        }

        logger.info(f"Fetching {item_type} data for league: {self.league}")
        return self._make_request(endpoint, params)

    def get_all_currency_data(self) -> Dict[str, Dict]:
        """
        Fetch all available currency data types

        Returns:
            Dictionary with all currency data
        """
        all_data = {}

        for currency_type in self.CURRENCY_TYPES.keys():
            data = self.get_currency_data(currency_type)
            if data:
                all_data[currency_type] = data

        return all_data


class CurrencyDataManager:
    """Manages storage and retrieval of currency price data"""

    def __init__(self, data_dir: str = "poe_data"):
        """
        Initialize the data manager

        Args:
            data_dir: Directory to store data files
        """
        self.data_dir = data_dir
        self.ensure_data_directory()

    def ensure_data_directory(self):
        """Create data directory if it doesn't exist"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            logger.info(f"Created data directory: {self.data_dir}")

    def save_currency_snapshot(self, data: Dict, timestamp: Optional[datetime] = None) -> str:
        """
        Save a currency data snapshot to file

        Args:
            data: Currency data to save
            timestamp: Timestamp for the snapshot (default: current time)

        Returns:
            Path to saved file
        """
        if timestamp is None:
            timestamp = datetime.now()

        filename = f"currency_snapshot_{timestamp.strftime('%Y%m%d_%H%M%S')}.json"
        filepath = os.path.join(self.data_dir, filename)

        # Add metadata
        snapshot = {
            'timestamp': timestamp.isoformat(),
            'data': data
        }

        with open(filepath, 'w') as f:
            json.dump(snapshot, f, indent=2)

        logger.info(f"Saved currency snapshot to: {filepath}")
        return filepath

    def load_currency_snapshot(self, filepath: str) -> Optional[Dict]:
        """
        Load a currency data snapshot from file

        Args:
            filepath: Path to the snapshot file

        Returns:
            Currency data dictionary or None if failed
        """
        try:
            with open(filepath, 'r') as f:
                snapshot = json.load(f)
            return snapshot
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Failed to load snapshot from {filepath}: {e}")
            return None

    def get_latest_snapshot(self) -> Optional[Dict]:
        """
        Get the most recent currency snapshot

        Returns:
            Latest snapshot data or None if no snapshots exist
        """
        snapshot_files = [f for f in os.listdir(self.data_dir)
                         if f.startswith('currency_snapshot_') and f.endswith('.json')]

        if not snapshot_files:
            logger.info("No currency snapshots found")
            return None

        # Sort by filename (which includes timestamp)
        latest_file = sorted(snapshot_files)[-1]
        filepath = os.path.join(self.data_dir, latest_file)

        return self.load_currency_snapshot(filepath)

    def get_all_snapshots(self) -> List[Dict]:
        """
        Load all currency snapshots

        Returns:
            List of all snapshot data
        """
        snapshot_files = [f for f in os.listdir(self.data_dir)
                         if f.startswith('currency_snapshot_') and f.endswith('.json')]

        snapshots = []
        for filename in sorted(snapshot_files):
            filepath = os.path.join(self.data_dir, filename)
            snapshot = self.load_currency_snapshot(filepath)
            if snapshot:
                snapshots.append(snapshot)

        return snapshots

    def convert_to_dataframe(self, currency_data: Dict, data_type: str = 'currency') -> pd.DataFrame:
        """
        Convert currency data to pandas DataFrame for analysis

        Args:
            currency_data: Raw currency data from API
            data_type: Type of data ('currency' or 'fragment')

        Returns:
            DataFrame with currency information
        """
        if 'lines' not in currency_data:
            logger.error("Invalid currency data format")
            return pd.DataFrame()

        lines = currency_data['lines']

        # Extract relevant fields
        records = []
        for line in lines:
            record = {
                'name': line.get('currencyTypeName', ''),
                'chaos_equivalent': line.get('chaosEquivalent', 0),
                'details_id': line.get('detailsId', ''),
                'data_type': data_type
            }

            # Add pay/receive data if available
            if 'pay' in line and line['pay']:
                record.update({
                    'pay_value': line['pay'].get('value', 0),
                    'pay_count': line['pay'].get('count', 0),
                    'pay_listing_count': line['pay'].get('listing_count', 0)
                })

            if 'receive' in line and line['receive']:
                record.update({
                    'receive_value': line['receive'].get('value', 0),
                    'receive_count': line['receive'].get('count', 0),
                    'receive_listing_count': line['receive'].get('listing_count', 0)
                })

            # Add sparkline data for trend analysis
            if 'paySparkLine' in line and 'totalChange' in line['paySparkLine']:
                record['pay_total_change'] = line['paySparkLine']['totalChange']

            if 'receiveSparkLine' in line and 'totalChange' in line['receiveSparkLine']:
                record['receive_total_change'] = line['receiveSparkLine']['totalChange']

            records.append(record)

        return pd.DataFrame(records)


def main():
    """Main function to demonstrate the currency analysis tool"""
    print("Path of Exile Currency Price Analysis Tool")
    print("=" * 50)

    # Initialize API client and data manager
    api = POENinjaAPI(league="Standard")  # Change league as needed
    data_manager = CurrencyDataManager()

    try:
        # Fetch current currency data
        print("Fetching current currency data...")
        currency_data = api.get_currency_data('currency')
        fragment_data = api.get_currency_data('fragment')

        if currency_data:
            print(f"✓ Fetched {len(currency_data.get('lines', []))} currency items")

            # Save snapshot
            all_data = {'currency': currency_data}
            if fragment_data:
                all_data['fragment'] = fragment_data
                print(f"✓ Fetched {len(fragment_data.get('lines', []))} fragment items")

            snapshot_path = data_manager.save_currency_snapshot(all_data)
            print(f"✓ Saved snapshot to: {snapshot_path}")

            # Convert to DataFrame and display top currencies by value
            df = data_manager.convert_to_dataframe(currency_data, 'currency')
            if not df.empty:
                print("\nTop 10 Most Valuable Currencies:")
                print("-" * 40)
                top_currencies = df.nlargest(10, 'chaos_equivalent')[['name', 'chaos_equivalent', 'pay_total_change']]
                for _, row in top_currencies.iterrows():
                    change_indicator = "📈" if row.get('pay_total_change', 0) > 0 else "📉" if row.get('pay_total_change', 0) < 0 else "➡️"
                    print(f"{row['name']:<25} {row['chaos_equivalent']:>10.2f} chaos {change_indicator}")

        else:
            print("❌ Failed to fetch currency data")

    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()