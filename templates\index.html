<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Overview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="icon" type="image/ico" href="{{ url_for('static', filename='favico.ico') }}">

</head>
<body>
    <nav class="nav-container">
        <div class="nav-menu">
            {% include 'nav.html' %}
            <div class="nav-info">
                <div class="ilvl-counter">
                    <span class="counter-value">0</span> Average iLvl
                </div>
                <span id="lastUpdated" class="text-white">Last updated: {{ last_updated }}</span>
            </div>
        </div>
    </nav>
    <div class="table-container">
        <div class="role-counters mb-3">
            <div class="d-flex">
                <div class="counter-group">
                    <div class="role-counter role-tank">
                        <img src="{{ url_for('static', filename='images/tank.png') }}" alt="Tank" class="role-icon">
                        <span class="counter-value">0</span> Tanks
                    </div>
                    <div class="role-counter role-healer">
                        <img src="{{ url_for('static', filename='images/healer.png') }}" alt="Healer" class="role-icon">
                        <span class="counter-value">0</span> Healers
                    </div>
                </div>
                <div class="counter-group">
                    <div class="role-counter role-melee">
                        <img src="{{ url_for('static', filename='images/melee.png') }}" alt="Melee" class="role-icon">
                        <span class="counter-value">0</span> Melee
                    </div>
                    <div class="role-counter role-ranged">
                        <img src="{{ url_for('static', filename='images/ranged.png') }}" alt="Ranged" class="role-icon">
                        <span class="counter-value">0</span> Ranged
                    </div>
                </div>
            </div>
        </div>
        <div class="tier-counters mb-3">
            <div class="d-flex">
                <div class="counter-group">
                    <div class="tier-counter tier-mystic">
                        <span class="counter-value">0</span> Mystic
                    </div>
                    <div class="tier-counter tier-dreadful">
                        <span class="counter-value">0</span> Dreadful
                    </div>
                    <div class="tier-counter tier-venerated">
                        <span class="counter-value">0</span> Venerated
                    </div>
                    <div class="tier-counter tier-zenith">
                        <span class="counter-value">0</span> Zenith
                    </div>
                </div>
                <div class="counter-group">
                    <div class="armor-counter armor-plate">
                        <span class="counter-value">0</span> Plate
                    </div>
                    <div class="armor-counter armor-mail">
                        <span class="counter-value">0</span> Mail
                    </div>
                    <div class="armor-counter armor-leather">
                        <span class="counter-value">0</span> Leather
                    </div>
                    <div class="armor-counter armor-cloth">
                        <span class="counter-value">0</span> Cloth
                    </div>
                </div>
            </div>
        </div>
        <div class="buff-counters mb-3">
            <div class="single-counter-group">
                <div class="buff-counter buff-druid" data-class="Druid" title="Mark of the Wild">
                    <img src="https://wow.zamimg.com/images/wow/icons/large/spell_nature_regeneration.jpg" alt="Mark of the Wild" class="buff-icon">
                </div>
                <div class="buff-counter buff-evoker" data-class="Evoker" title="Blessing of the Bronze">
                    <img src="https://wow.zamimg.com/images/wow/icons/large/ability_evoker_blessingofthebronze.jpg" alt="Blessing of the Bronze" class="buff-icon">
                </div>
                <div class="buff-counter buff-hunter" data-class="Hunter" title="Hunter's Mark">
                    <img src="https://wow.zamimg.com/images/wow/icons/large/ability_hunter_snipershot.jpg" alt="Hunter's Mark" class="buff-icon">
                </div>
                <div class="buff-counter buff-mage" data-class="Mage" title="Arcane Intellect">
                    <img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_magicalsentry.jpg" alt="Arcane Intellect" class="buff-icon">
                </div>
                <div class="buff-counter buff-priest" data-class="Priest" title="Power Word: Fortitude">
                    <img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_wordfortitude.jpg" alt="Power Word: Fortitude" class="buff-icon">
                </div>
                <div class="buff-counter buff-shaman" data-class="Shaman" title="Skyfury">
                    <img src="https://wow.zamimg.com/images/wow/icons/large/achievement_raidprimalist_windelemental.jpg" alt="Skyfury" class="buff-icon">
                </div>
                <div class="buff-counter buff-warrior" data-class="Warrior" title="Battle Shout">
                    <img src="https://wow.zamimg.com/images/wow/icons/large/ability_warrior_battleshout.jpg" alt="Battle Shout" class="buff-icon">
                </div>
                <div class="debuff-counter debuff-demonhunter" data-class="Demon Hunter" title="Chaos Brand">
                    <img src="https://wow.zamimg.com/images/wow/icons/large/ability_demonhunter_empowerwards.jpg" alt="Chaos Brand" class="debuff-icon">
                </div>
                <div class="debuff-counter debuff-monk" data-class="Monk" title="Mystic Touch">
                    <img src="https://wow.zamimg.com/images/wow/icons/large/ability_monk_palmstrike.jpg" alt="Mystic Touch" class="debuff-icon">
                </div>
            </div>
        </div>

        <table class="rank-table">
            <thead>
                <tr>
                    <th class="sortable" data-sort="character-name">Character</th>
                    <th class="sortable" data-sort="realm">Realm</th>
                    <th class="sortable" data-sort="ilvl">iLvl</th>
                    <th class="sortable" data-sort="role">Role</th>
                    <th class="sortable" data-sort="class">Class</th>
                    <th class="sortable" data-sort="spec">Spec</th>
                    <th class="sortable" data-sort="armor">Armor</th>
                    <th class="sortable" data-sort="tier">Tier</th>
                </tr>
            </thead>
            <tbody>
                {% for character in characters %}
                <tr>
                    <td>
                        <div class="character-name">
                            <a href="{{ character.url }}" target="_blank">{{ character.name }}</a>
                                       
                        </div>
                    </td>
                    <td>{{ character.realm }}</td>
                    <td class="ilvl">{{ character.ilvl }}</td>
                    <td class="role role-{{ character.role.lower() }}">
                        {% if character.role == "Tank" %}
                            <img src="https://cdn.raiderio.net/assets/img/role_tank-6cee7610058306ba277e82c392987134.png" alt="{{ character.role }}" class="role-icon">
                        {% elif character.role == "Healer" %}
                            <img src="https://cdn.raiderio.net/assets/img/role_healer-984e5e9867d6508a714a9c878d87441b.png" alt="{{ character.role }}" class="role-icon">
                        {% else %}
                            <img src="https://cdn.raiderio.net/assets/img/role_dps-eb25989187d4d3ac866d609dc009f090.png" alt="{{ character.role }}" class="role-icon">
                        {% endif %}
                        {{ character.role }}
                    </td>
                    <td class="class-{{ character.class_name.lower().replace(' ', '') }}">{{ character.class_name }}</td>
                    <td class="spec">{{ character.spec }}</td>
                    <td class="armor-type armor-type-{{ character.armor_type.lower() }}">{{ character.armor_type }}</td>
                    <td class="tier-token tier-token-{{ character.tier_token.lower() }}">{{ character.tier_token }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const table = document.querySelector('.rank-table');
            const headers = table.querySelectorAll('th.sortable');
            let currentSort = { column: null, asc: true };

            function updateCounters() {
                const rows = document.querySelectorAll('.rank-table tbody tr:not([style*="display: none"])');
                const roleCounters = {
                    'Tank': 0,
                    'Healer': 0,
                    'Melee': 0,
                    'Ranged': 0
                };
                const tierCounters = {
                    'Mystic': 0,
                    'Dreadful': 0,
                    'Venerated': 0,
                    'Zenith': 0
                };
                const armorCounters = {
                    'Plate': 0,
                    'Mail': 0,
                    'Leather': 0,
                    'Cloth': 0
                };

                const availableClasses = new Set();

                let totalIlvl = 0;
                let characterCount = 0;
                
                rows.forEach(row => {
                    // Count roles
                    const roleCell = row.querySelector('.role');
                    const roleText = roleCell.textContent.replace(/[\n\r]+|[\s]{2,}/g, ' ').trim();
                    
                    if (roleText.includes('Tank')) roleCounters['Tank']++;
                    else if (roleText.includes('Healer')) roleCounters['Healer']++;
                    else if (roleText.includes('Melee')) roleCounters['Melee']++;
                    else if (roleText.includes('Ranged')) roleCounters['Ranged']++;

                    // Count tier tokens
                    const tierCell = row.querySelector('.tier-token');
                    const tierText = tierCell.textContent.trim();
                    if (tierCounters.hasOwnProperty(tierText)) {
                        tierCounters[tierText]++;
                    }

                    // Count armor types
                    const armorCell = row.querySelector('.armor-type');
                    const armorText = armorCell.textContent.trim();
                    if (armorCounters.hasOwnProperty(armorText)) {
                        armorCounters[armorText]++;
                    }

                    // Track available classes for buff/debuff counters
                    const classCell = row.querySelector('td:nth-child(5)'); // Class column
                    const className = classCell.textContent.trim();
                    availableClasses.add(className);

                    // Calculate average ilvl
                    const ilvlCell = row.querySelector('.ilvl');
                    if (ilvlCell) {
                        const ilvl = parseInt(ilvlCell.textContent.trim());
                        if (!isNaN(ilvl)) {
                            totalIlvl += ilvl;
                            characterCount++;
                        }
                    }
                });

                // Update role counters
                Object.entries(roleCounters).forEach(([role, count]) => {
                    const counter = document.querySelector(`.role-${role.toLowerCase()} .counter-value`);
                    if (counter) {
                        counter.textContent = count;
                    }
                });

                // Update tier counters
                Object.entries(tierCounters).forEach(([tier, count]) => {
                    const counter = document.querySelector(`.tier-${tier.toLowerCase()} .counter-value`);
                    if (counter) {
                        counter.textContent = count;
                    }
                });

                // Update armor counters
                Object.entries(armorCounters).forEach(([armor, count]) => {
                    const counter = document.querySelector(`.armor-${armor.toLowerCase()} .counter-value`);
                    if (counter) {
                        counter.textContent = count;
                    }
                });

                // Update average ilvl counter
                const avgIlvl = characterCount > 0 ? Math.round(totalIlvl / characterCount) : 0;
                const ilvlCounter = document.querySelector('.ilvl-counter .counter-value');
                if (ilvlCounter) {
                    ilvlCounter.textContent = avgIlvl;
                }

                // Update buff/debuff counters
                const buffCounters = document.querySelectorAll('.buff-counter, .debuff-counter');
                buffCounters.forEach(counter => {
                    const requiredClass = counter.dataset.class;
                    if (availableClasses.has(requiredClass)) {
                        counter.classList.remove('unavailable');
                        counter.classList.add('available');
                    } else {
                        counter.classList.remove('available');
                        counter.classList.add('unavailable');
                    }
                });
            }

            // Initialize buff counters as unavailable
            const buffCounters = document.querySelectorAll('.buff-counter, .debuff-counter');
            buffCounters.forEach(counter => {
                counter.classList.add('unavailable');
            });

            // Sort table by role on page load
            function sortTableByRole() {
                const tbody = table.querySelector('tbody');
                const rows = Array.from(tbody.querySelectorAll('tr'));

                // Define role order priority
                const roleOrder = {
                    'Tank': 1,
                    'Healer': 2,
                    'Melee': 3,
                    'Ranged': 4
                };

                // Sort rows by role priority
                rows.sort((a, b) => {
                    const roleA = a.querySelector('.role').textContent.replace(/[\n\r]+|[\s]{2,}/g, ' ').trim();
                    const roleB = b.querySelector('.role').textContent.replace(/[\n\r]+|[\s]{2,}/g, ' ').trim();

                    // Extract role name (remove extra whitespace and get the role)
                    let roleNameA = 'Unknown';
                    let roleNameB = 'Unknown';

                    if (roleA.includes('Tank')) roleNameA = 'Tank';
                    else if (roleA.includes('Healer')) roleNameA = 'Healer';
                    else if (roleA.includes('Melee')) roleNameA = 'Melee';
                    else if (roleA.includes('Ranged')) roleNameA = 'Ranged';

                    if (roleB.includes('Tank')) roleNameB = 'Tank';
                    else if (roleB.includes('Healer')) roleNameB = 'Healer';
                    else if (roleB.includes('Melee')) roleNameB = 'Melee';
                    else if (roleB.includes('Ranged')) roleNameB = 'Ranged';

                    const priorityA = roleOrder[roleNameA] || 999;
                    const priorityB = roleOrder[roleNameB] || 999;

                    return priorityA - priorityB;
                });

                // Reorder rows in the table
                rows.forEach(row => tbody.appendChild(row));

                // Update sort indicator on role header
                headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
                const roleHeader = document.querySelector('[data-sort="role"]');
                if (roleHeader) {
                    roleHeader.classList.add('sort-asc');
                }

                // Set current sort state
                currentSort = { column: 'role', asc: true };
            }

            // Sort by role on page load
            sortTableByRole();

            // Update counters initially
            updateCounters();

            headers.forEach(header => {
                header.addEventListener('click', () => {
                    const column = header.dataset.sort;
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr'));
                    
                    // Update sort direction
                    if (currentSort.column === column) {
                        currentSort.asc = !currentSort.asc;
                    } else {
                        currentSort = { column: column, asc: true };
                    }
                    
                    // Remove existing arrows
                    headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
                    // Add new arrow
                    header.classList.add(currentSort.asc ? 'sort-asc' : 'sort-desc');

                    // Sort rows
                    rows.sort((a, b) => {
                        let aVal, bVal;
                        if (column === 'character-name') {
                            aVal = a.querySelector('.character-name').textContent.trim();
                            bVal = b.querySelector('.character-name').textContent.trim();
                        } else {
                            aVal = a.querySelector(`td:nth-child(${Array.from(headers).indexOf(header) + 1})`).textContent.trim();
                            bVal = b.querySelector(`td:nth-child(${Array.from(headers).indexOf(header) + 1})`).textContent.trim();
                        }
                        return currentSort.asc ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
                    });

                    // Reorder rows
                    rows.forEach(row => tbody.appendChild(row));
                    
                    // Update counters after the rows are reordered
                    updateCounters();
                });
            });

            // Search functionality
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const tbody = table.querySelector('tbody');
                const rows = tbody.querySelectorAll('tr');

                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(searchTerm) ? '' : 'none';
                });
                
                // Update counters when search is performed
                updateCounters();
            });
        });
    </script>
</body>
</html> 
