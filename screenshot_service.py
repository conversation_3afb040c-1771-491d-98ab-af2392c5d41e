import os
import time
import schedule
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from PIL import Image, ImageOps

# Create screenshots directory if it doesn't exist
SCREENSHOTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'screenshots')
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

def invert_image_colors(image_path):
    """Invert the colors of an image to create black background with white text"""
    try:
        # Open the image
        image = Image.open(image_path)

        # Convert to RGB if not already
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Invert the colors
        inverted_image = ImageOps.invert(image)

        # Save the inverted image
        inverted_image.save(image_path)
        print(f"Applied color inversion to: {image_path}")

    except Exception as e:
        print(f"Error inverting image colors: {str(e)}")

def take_screenshot(page_type='raiders'):
    try:
        # Set up Chrome options
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--window-size=2560,1440')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # Add retry logic for connecting to Selenium
        max_retries = 3
        retry_delay = 5  # seconds
        
        for attempt in range(max_retries):
            try:
                # Connect to Selenium standalone container
                driver = webdriver.Remote(
                    command_executor='http://localhost:4444/wd/hub',
                    options=chrome_options
                )
                print("Connected to Selenium container successfully")
                break
            except Exception as e:
                if attempt == max_retries - 1:  # Last attempt
                    raise Exception(f"Failed to connect to Selenium after {max_retries} attempts: {str(e)}")
                print(f"Connection attempt {attempt + 1} failed, retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
        
        # Add longer wait time for page load
        driver.set_page_load_timeout(30)
        
        # Navigate to the appropriate page based on page_type
        if page_type == 'tier':
            base_url = 'https://portal.uproar-guild.com'
            driver.get(f'{base_url}/tier')
            wait_element = "table-container"
            wait_by = By.CLASS_NAME
            filename = 'screenshot_tier.png'
            element_screenshot = True
        elif page_type == 'enchants':
            base_url = 'https://portal.uproar-guild.com'
            driver.get(f'{base_url}/enchants')
            wait_element = "table-container"
            wait_by = By.CLASS_NAME
            filename = 'screenshot_enchants.png'
            element_screenshot = True
        elif page_type == 'raid_rules':
            driver.get('https://guildsofwow.com/uproarstormscale/post/7018/raids-rules')
            wait_element = "post-body"
            wait_by = By.ID
            filename = 'screenshot_rules.png'
            element_screenshot = True
        else:  # default to raiders
            base_url = 'https://portal.uproar-guild.com'
            driver.get(base_url)
            wait_element = "table-container"
            wait_by = By.CLASS_NAME
            filename = 'screenshot_raiders.png'
            element_screenshot = True

        # Wait for the element to be present with increased timeout
        element = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((wait_by, wait_element))
        )

        # Add small delay to ensure page is fully rendered
        time.sleep(2)

        filepath = os.path.join(SCREENSHOTS_DIR, filename)

        # Take screenshot - either full page or specific element
        if element_screenshot:
            element.screenshot(filepath)
        else:
            driver.save_screenshot(filepath)

        # Apply color inversion for raid rules to get black background with white text
        if page_type == 'raid_rules':
            invert_image_colors(filepath)

        print(f"Screenshot saved: {filepath}")

    except Exception as e:
        print(f"Error taking screenshot of {page_type} page: {str(e)}")
    
    finally:
        if 'driver' in locals():
            try:
                driver.quit()
            except:
                print("Error while closing driver")

def main():
    # Schedule the screenshot tasks to run every hour
    schedule.every().hour.at(":00").do(take_screenshot, page_type='raiders')
    schedule.every().hour.at(":05").do(take_screenshot, page_type='tier')
    schedule.every().hour.at(":10").do(take_screenshot, page_type='enchants')
    schedule.every().hour.at(":15").do(take_screenshot, page_type='raid_rules')

    print("Screenshot service started. Taking screenshots every hour...")

    # Run the first screenshots immediately
    take_screenshot('raiders')
    take_screenshot('tier')
    take_screenshot('enchants')
    take_screenshot('raid_rules')
    
    # Keep the script running
    while True:
        schedule.run_pending()
        time.sleep(60)  # Check every minute for pending tasks

if __name__ == "__main__":
    main() 
