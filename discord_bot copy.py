import os
import discord
from discord.ext import commands, tasks
from dotenv import load_dotenv
import csv
from datetime import datetime

# Load environment variables
load_dotenv()
TOKEN = os.getenv('DISCORD_TOKEN')

# Set up bot with intents
intents = discord.Intents.default()
intents.members = True  # Enable member intents
intents.message_content = True  # Enable message content intent
bot = commands.Bot(command_prefix='!!', intents=intents)

# Create exports directory if it doesn't exist
EXPORTS_DIR = "discord_exports"
if not os.path.exists(EXPORTS_DIR):
    os.makedirs(EXPORTS_DIR)

# Target server ID
TARGET_SERVER_ID = 451810734129676298

# Fixed export filename
EXPORT_FILENAME = "csv_user_export.csv"

@bot.event
async def on_ready():
    print(f'{bot.user} has connected to Discord!')
    # Start the hourly export task
    hourly_export.start()

@tasks.loop(hours=1)
async def hourly_export():
    """Automatically export users every hour"""
    guild = bot.get_guild(TARGET_SERVER_ID)
    if guild is None:
        print(f"Could not find server with ID {TARGET_SERVER_ID}")
        return

    filename = os.path.join(EXPORTS_DIR, EXPORT_FILENAME)
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['Username', 'Display Name', 'ID', 'Roles', 'Raider']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for member in guild.members:
                # Get all roles for the member
                roles = [role.name for role in member.roles if role.name != '@everyone']
                is_raider = 'Uproar Raider' in roles
                
                # Write all users to CSV
                writer.writerow({
                    'Username': str(member),  # Full username
                    'Display Name': member.display_name,
                    'ID': str(member.id),  # Ensure ID is string
                    'Roles': ', '.join(roles) if roles else '',
                    'Raider': str(is_raider)  # 'True' or 'False'
                })
                print(f"Exported user: {member.display_name}")
                        
        print(f"Hourly export completed: {filename}")
        
    except Exception as e:
        print(f"Error during automated export: {str(e)}")

@bot.command(name='export_users')
async def export_users(ctx):
    """Export all users from the server to a CSV file"""
    # Check if the command is used in the target server
    if ctx.guild.id != TARGET_SERVER_ID:
        await ctx.send(f"This command can only be used in the target server (ID: {TARGET_SERVER_ID})!")
        return

    # Use fixed filename
    filename = os.path.join(EXPORTS_DIR, EXPORT_FILENAME)
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['Username', 'Display Name', 'ID', 'Roles']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            # Write each member's information
            for member in ctx.guild.members:
                roles = [role.name for role in member.roles if role.name != '@everyone']
                writer.writerow({
                    'Username': str(member),
                    'Display Name': member.display_name,
                    'ID': member.id,
                    'Roles': ', '.join(roles)
                })
        await ctx.send(f"User export complete! File saved as: {filename}")
        
    except Exception as e:
        await ctx.send(f"Error during export: {str(e)}")

@bot.command(name='raiders')
async def raiders(ctx):
    """Post the raiders screenshot"""
    try:
        # Send the screenshot file
        with open('screenshots/screenshot_raiders.png', 'rb') as f:
            picture = discord.File(f)
            await ctx.send(file=picture)
    except Exception as e:
        await ctx.send(f"Error sending raiders screenshot: {str(e)}")

@bot.command(name='tier')
async def tier(ctx):
    """Post the tier screenshot"""
    try:
        # Send the screenshot file
        with open('screenshots/screenshot_tier.png', 'rb') as f:
            picture = discord.File(f)
            await ctx.send(file=picture)
    except Exception as e:
        await ctx.send(f"Error sending tier screenshot: {str(e)}")

@bot.command(name='enchants')
async def enchants(ctx):
    """Post the enchants screenshot"""
    try:
        # Send the screenshot file
        with open('screenshots/screenshot_enchants.png', 'rb') as f:
            picture = discord.File(f)
            await ctx.send(file=picture)
    except Exception as e:
        await ctx.send(f"Error sending enchants screenshot: {str(e)}")

@bot.command(name='rules')
async def rules(ctx):
    """Post the rules screenshot"""
    try:
        # Send the screenshot file
        with open('screenshots/screenshot_rules.png', 'rb') as f:
            picture = discord.File(f)
            await ctx.send(file=picture)
    except Exception as e:
        await ctx.send(f"Error sending raiders screenshot: {str(e)}")

# Run the bot
bot.run(TOKEN) 
