{% extends "poe_base.html" %}

{% block title %}POE Currency Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-coins"></i> Currency Market Dashboard</h1>
        <p class="text-muted">Real-time Path of Exile currency prices and market analysis</p>
        
        {% if error %}
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> Error: {{ error }}
        </div>
        {% endif %}
        
        {% if last_updated %}
        <div class="alert alert-info">
            <i class="fas fa-clock"></i> Last updated: {{ last_updated }}
        </div>
        {% endif %}
    </div>
</div>

<!-- Market Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title"><i class="fas fa-coins"></i> Total Currencies</h5>
                <h3 class="currency-value">{{ analysis_summary.get('total_currencies', 0) }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title"><i class="fas fa-chart-line"></i> Opportunities</h5>
                <h3 class="currency-value">{{ analysis_summary.get('opportunities_count', 0) }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title"><i class="fas fa-arrow-up"></i> Top Gainers</h5>
                <h3 class="price-up">{{ analysis_summary.get('top_gainers', [])|length }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title"><i class="fas fa-arrow-down"></i> Top Losers</h5>
                <h3 class="price-down">{{ analysis_summary.get('top_losers', [])|length }}</h3>
            </div>
        </div>
    </div>
</div>

<!-- Current Prices -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> Top Currencies by Value</h5>
            </div>
            <div class="card-body">
                {% if current_data %}
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Currency</th>
                                <th>Value (Chaos)</th>
                                <th>Trend</th>
                                <th>Change</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for currency in current_data %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ currency.name }}</td>
                                <td class="currency-value">{{ "%.2f"|format(currency.chaos_equivalent) }}</td>
                                <td>
                                    {% if currency.pay_total_change %}
                                        {% if currency.pay_total_change > 0 %}📈
                                        {% elif currency.pay_total_change < 0 %}📉
                                        {% else %}➡️{% endif %}
                                    {% else %}➡️{% endif %}
                                </td>
                                <td>
                                    {% if currency.pay_total_change %}
                                        <span class="{% if currency.pay_total_change > 0 %}price-up{% elif currency.pay_total_change < 0 %}price-down{% else %}price-neutral{% endif %}">
                                            {{ "%.1f"|format(currency.pay_total_change) }}%
                                        </span>
                                    {% else %}
                                        <span class="price-neutral">N/A</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-database fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No currency data available. Click "Collect Data" to fetch current prices.</p>
                    <button class="btn btn-primary" onclick="collectData()">
                        <i class="fas fa-sync-alt"></i> Collect Data Now
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Analysis Summary -->
    <div class="col-lg-4">
        <!-- Top Gainers -->
        {% if analysis_summary.get('top_gainers') %}
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="fas fa-arrow-up"></i> Top Gainers</h6>
            </div>
            <div class="card-body">
                {% for gainer in analysis_summary.top_gainers[:3] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <small>{{ gainer.name[:20] }}{% if gainer.name|length > 20 %}...{% endif %}</small>
                    <span class="price-up">+{{ "%.1f"|format(gainer.price_change) }}%</span>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Top Losers -->
        {% if analysis_summary.get('top_losers') %}
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="fas fa-arrow-down"></i> Top Losers</h6>
            </div>
            <div class="card-body">
                {% for loser in analysis_summary.top_losers[:3] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <small>{{ loser.name[:20] }}{% if loser.name|length > 20 %}...{% endif %}</small>
                    <span class="price-down">{{ "%.1f"|format(loser.price_change) }}%</span>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Trading Opportunities -->
        {% if analysis_summary.get('opportunities') %}
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-bullseye"></i> Trading Opportunities</h6>
            </div>
            <div class="card-body">
                {% for opp in analysis_summary.opportunities[:3] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <small>{{ opp.name[:15] }}{% if opp.name|length > 15 %}...{% endif %}</small>
                        <br>
                        <span class="badge {% if opp.opportunity_type == 'BUY' %}bg-success{% elif opp.opportunity_type == 'SELL' %}bg-danger{% else %}bg-secondary{% endif %}">
                            {{ opp.opportunity_type }}
                        </span>
                    </div>
                    <span class="{% if opp.price_change > 0 %}price-up{% else %}price-down{% endif %}">
                        {{ "%.1f"|format(opp.price_change) }}%
                    </span>
                </div>
                {% endfor %}
                {% if analysis_summary.opportunities|length > 3 %}
                <div class="text-center mt-2">
                    <a href="/analysis" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-tools"></i> Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/analysis" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-chart-line"></i> Detailed Analysis
                    </a>
                    <a href="/charts" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-chart-bar"></i> View Charts
                    </a>
                    <a href="/data-collection" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-database"></i> Manage Data
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Auto-refresh notice -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> 
            <strong>Tip:</strong> This dashboard shows real-time data from poe.ninja. 
            Use the "Collect Data" button to fetch the latest prices, or set up automated collection 
            in the <a href="/data-collection" class="alert-link">Data Collection</a> section.
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh data every 5 minutes
    setInterval(function() {
        const now = new Date();
        const minutes = now.getMinutes();
        
        // Only auto-refresh at 5-minute intervals to avoid spam
        if (minutes % 5 === 0 && now.getSeconds() < 10) {
            console.log('Auto-refreshing data...');
            collectData();
        }
    }, 10000); // Check every 10 seconds
    
    // Add tooltips to currency names
    document.addEventListener('DOMContentLoaded', function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
